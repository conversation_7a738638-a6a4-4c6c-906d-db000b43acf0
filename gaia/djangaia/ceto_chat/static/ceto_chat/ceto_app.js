// Ceto Chat JavaScript v2
console.log('Ceto Chat app v2 loading...');

// Initialize Vue app with debug panel functionality
// No need for DOMContentLoaded since script is loaded with defer
const { createApp } = Vue;

const app = createApp({
    // Mix in debug panel functionality
    mixins: [window.DebugPanelMixin],

    data() {
        return {
            appVersion: '2.0.0',
            appName: 'Ceto Chat',
            // MCP-related data
            mcpTools: [],
            mcpServerUrl: '',
            mcpConnected: false,
            mcpLoading: false,
            // Level 0032: Tool testing
            testMessage: 'Hello from frontend!',
            mcpToolLoading: false,
            lastToolResponse: null
        };
    },

    methods: {
        // Level 0031: Load MCP tools from server
        async loadMcpTools() {
            console.log('🔵 loadMcpTools() called');
            this.mcpLoading = true;
            this.addDebugLog('MCP', 'SENT', 'Loading MCP tools...');

            const callDetail = this.addCallDetail('GET', '/ceto_chat/api/mcp/tools/');

            try {
                console.log('🔵 Fetching MCP tools from /ceto_chat/api/mcp/tools/');
                const response = await fetch('/ceto_chat/api/mcp/tools/');
                console.log('🔵 MCP tools response status:', response.status);
                const data = await response.json();
                console.log('🔵 MCP tools data:', data);

                if (response.ok && data.success) {
                    console.log('🟢 MCP tools loaded successfully');
                    this.mcpTools = data.tools || [];
                    this.mcpServerUrl = data.server_url || '';
                    this.mcpConnected = true;

                    this.updateCallDetail(callDetail, response.status, data);
                    this.addDebugLog('MCP', 'RECEIVED', `Loaded ${this.mcpTools.length} tools from ${this.mcpServerUrl}`);

                    console.log('MCP tools loaded:', this.mcpTools);
                    console.log('MCP server URL:', this.mcpServerUrl);
                    console.log('MCP connected:', this.mcpConnected);
                } else {
                    console.log('🔴 Failed to load MCP tools:', data.error);
                    this.mcpConnected = false;
                    this.updateCallDetail(callDetail, response.status, data, data.error);
                    this.addErrorLog(data.error || 'Failed to load MCP tools', 'loadMcpTools');
                }
            } catch (error) {
                this.mcpConnected = false;
                this.updateCallDetail(callDetail, 'ERROR', null, error.message);
                this.addErrorLog(`Network error loading MCP tools: ${error.message}`, 'loadMcpTools');
                console.error('Error loading MCP tools:', error);
            } finally {
                this.mcpLoading = false;
            }
        },

        // Level 0034: Call Echostring MCP Tool
        async callEchostringTool() {
                console.log('BUTTON WAS PRESSED!');
                console.log('� BUTTON PRESSED! callEchostringTool() called!');
                console.log('�🔵 callEchostringTool() called!');
                console.log('🔵 testMessage:', this.testMessage);

                if (!this.testMessage.trim()) {
                    console.log('🔴 Empty message, returning early');
                    this.addErrorLog('Please enter a message to echo', 'callEchostringTool');
                    return;
                }

                console.log('🔵 Setting loading state and clearing previous response');
                this.mcpToolLoading = true;
                this.lastToolResponse = null;

                const requestData = {
                    tool_name: 'echostring',
                    tool_args: {
                        text: this.testMessage
                    }
                };

                console.log('🔵 Request data:', requestData);
                this.addDebugLog('MCP_TOOL', 'SENT', `Calling echostring with: "${this.testMessage}"`);
                const callDetail = this.addCallDetail('POST', '/ceto_chat/api/mcp/call/', requestData);

                try {
                    console.log('🔵 Making fetch request to /ceto_chat/api/mcp/call/');
                    const response = await fetch('/ceto_chat/api/mcp/call/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log('🔵 Response status:', response.status);
                    const data = await response.json();
                    console.log('🔵 Response data:', data);

                    if (response.ok && data.success) {
                        console.log('🟢 Success! Tool result:', data.result);
                        this.lastToolResponse = {
                            success: true,
                            result: data.result,
                            tool_name: data.tool_name,
                            tool_args: data.tool_args
                        };

                        this.updateCallDetail(callDetail, response.status, data);
                        this.addDebugLog('MCP_TOOL', 'RECEIVED', `Echo response: "${data.result}"`);

                        console.log('Echo tool response:', data);
                    } else {
                        console.log('🔴 Error response:', data.error);
                        this.lastToolResponse = {
                            success: false,
                            error: data.error || 'Failed to call echo tool',
                            tool_name: data.tool_name,
                            tool_args: data.tool_args
                        };

                        this.updateCallDetail(callDetail, response.status, data, data.error);
                        this.addErrorLog(data.error || 'Failed to call echo tool', 'callEchostringTool');
                    }
                } catch (error) {
                    console.log('🔴 Network error:', error);
                    this.lastToolResponse = {
                        success: false,
                        error: `Network error: ${error.message}`
                    };

                    this.updateCallDetail(callDetail, 'ERROR', null, error.message);
                    this.addErrorLog(`Network error calling echo tool: ${error.message}`, 'callEchostringTool');
                    console.error('Error calling echo tool:', error);
                } finally {
                    console.log('🔵 Setting mcpToolLoading = false');
                    this.mcpToolLoading = false;
                }
            },

        // Test function to demonstrate debug logging
        testDebugPanel() {
            this.addDebugLog('TEST', 'SENT', { message: 'Test debug panel functionality' });
            this.addErrorLog('This is a test error message', 'testDebugPanel');

            const callDetail = this.addCallDetail('POST', '/api/test-endpoint', { test: true });
            setTimeout(() => {
                this.updateCallDetail(callDetail, 200, { success: true, data: 'Test response' });
            }, 1000);
        }
    },

    mounted() {
        console.log(`${this.appName} v${this.appVersion} initialized`);

        // Add initial debug log
        this.addDebugLog('SYSTEM', 'INFO', `${this.appName} v${this.appVersion} started`);

        // Level 0031: Load MCP tools on startup
        this.loadMcpTools();

        // Test debug panel after 3 seconds (after MCP tools load)
        setTimeout(() => {
            this.testDebugPanel();
        }, 3000);
    }
    });

// Mount the app
app.mount('#app');

console.log('Ceto Chat app v2 mounted successfully');
