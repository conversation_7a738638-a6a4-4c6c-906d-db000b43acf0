// Ceto Chat JavaScript v2 - Simplified
console.log('Ceto Chat app v2 (simplified) loading...');

// Initialize Vue app - no DOMContentLoaded wrapper needed since script is deferred
const { createApp } = Vue;

const app = createApp({
    data() {
        return {
            appVersion: '2.0.0',
            appName: 'Ceto Chat',
            testMessage: 'Hello from frontend!',
            mcpToolLoading: false
        };
    },

    methods: {
        // Call Echostring MCP Tool
        async callEchostringTool() {
            console.log('🎯 BUTTON WAS PRESSED! callEchostringTool() called!');
            console.log('🔵 testMessage:', this.testMessage);

            // Just log for now - no actual API call
            console.log('✅ Echo button pressed successfully!');
        }
    },

    mounted() {
        console.log(`${this.appName} v${this.appVersion} initialized (simplified)`);
    }
});

// Mount the app
app.mount('#app');

console.log('Ceto Chat app v2 (simplified) mounted successfully');
