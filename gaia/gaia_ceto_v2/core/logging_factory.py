"""
Centralized logging factory for Level 0007/0020 compliance.

This module eliminates duplicate logging setup code across core modules
while maintaining exact compatibility with LEVELS.md requirements.

Level 0007: LLM wrapper logging (/tmp/gaia_logs/ceto/llmwrap*.log)
Level 0020: MCP wrapper logging (/tmp/gaia_logs/ceto/mcpwrap*.log)
"""

import logging
from typing import Tuple
from pathlib import Path

# Import GAIA settings
try:
    from gaia_ceto_v2.settings import GAIA_SETTINGS
except ImportError:
    # Fallback if gaia_ceto_v2.settings is not available
    try:
        from django.conf import settings
        GAIA_SETTINGS = getattr(settings, 'GAIA_SETTINGS', None)
        if GAIA_SETTINGS is None:
            # Create a simple fallback object with required attributes
            class FallbackSettings:
                GAIA_LOGS_DIR = "/tmp/gaia_logs/ceto"
            GAIA_SETTINGS = FallbackSettings()
    except ImportError:
        # Final fallback
        class FallbackSettings:
            GAIA_LOGS_DIR = "/tmp/gaia_logs/ceto"
        GAIA_SETTINGS = FallbackSettings()


class FlushingFileHandler(logging.FileHandler):
    """FileHandler that flushes after every emit for immediate log visibility."""
    
    def emit(self, record):
        """Emit a record and immediately flush to ensure real-time logging."""
        super().emit(record)
        self.flush()


def create_dedicated_loggers(service_name: str, 
                           log_dir: str = None) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Create dedicated loggers for Level 0007/0020 compliance.
    
    Creates three loggers following the exact pattern required by LEVELS.md:
    - Main wrapper logger: {service_name}.log
    - Send logger: {service_name}_send.log  
    - Receive logger: {service_name}_rcv.log
    
    Args:
        service_name: Service name (e.g., 'llmwrap', 'mcpwrap')
        log_dir: Directory for log files (defaults to GAIA_SETTINGS.GAIA_LOGS_DIR)
        
    Returns:
        Tuple of (main_logger, send_logger, receive_logger)
    """
    log_dir = Path(log_dir or GAIA_SETTINGS.GAIA_LOGS_DIR)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Main service wrapper logger
    wrap_logger = logging.getLogger(service_name)
    wrap_logger.setLevel(logging.INFO)
    if not wrap_logger.handlers:
        handler = FlushingFileHandler(log_dir / f'{service_name}.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        wrap_logger.addHandler(handler)
    
    # Send logger (outgoing requests/calls)
    send_logger = logging.getLogger(f'{service_name}.send')
    send_logger.setLevel(logging.INFO)
    if not send_logger.handlers:
        handler = FlushingFileHandler(log_dir / f'{service_name}_send.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        send_logger.addHandler(handler)
    
    # Receive logger (incoming responses)
    rcv_logger = logging.getLogger(f'{service_name}.rcv')
    rcv_logger.setLevel(logging.INFO)
    if not rcv_logger.handlers:
        handler = FlushingFileHandler(log_dir / f'{service_name}_rcv.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        rcv_logger.addHandler(handler)
    
    return wrap_logger, send_logger, rcv_logger


def create_llm_loggers(log_dir: str = None) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Create LLM wrapper loggers for Level 0007 compliance.
    
    Creates loggers for LLM calls:
    - /tmp/gaia_logs/ceto/llmwrap.log
    - /tmp/gaia_logs/ceto/llmwrap_send.log  
    - /tmp/gaia_logs/ceto/llmwrap_rcv.log
    
    Returns:
        Tuple of (wrap_logger, send_logger, rcv_logger)
    """
    return create_dedicated_loggers('llmwrap', log_dir)


def create_mcp_loggers(log_dir: str = None) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Create MCP wrapper loggers for Level 0020 compliance.
    
    Creates loggers for MCP calls:
    - /tmp/gaia_logs/ceto/mcpwrap.log
    - /tmp/gaia_logs/ceto/mcpwrap_send.log
    - /tmp/gaia_logs/ceto/mcpwrap_rcv.log
    
    Returns:
        Tuple of (wrap_logger, send_logger, rcv_logger)
    """
    return create_dedicated_loggers('mcpwrap', log_dir)


def create_vendor_loggers(vendor_name: str, log_dir: str = None) -> Tuple[logging.Logger, logging.Logger]:
    """Create vendor API loggers for raw external API logging.
    
    Creates loggers for raw vendor API interactions:
    - /tmp/gaia_logs/ceto/vendor_{vendor_name}.log - Raw requests
    - /tmp/gaia_logs/ceto/vendor_{vendor_name}_resp.log - Raw responses
    
    Special case for 'cache' vendor:
    - /tmp/gaia_logs/ceto/cache_read.log - Cache read operations
    - /tmp/gaia_logs/ceto/cache_write.log - Cache write operations
    
    Args:
        vendor_name: Vendor name (e.g., 'exa', 'openai', 'anthropic', 'cache')
        log_dir: Directory for log files
        
    Returns:
        Tuple of (request_logger, response_logger) or (read_logger, write_logger) for cache
    """
    log_dir = Path(log_dir or GAIA_SETTINGS.GAIA_LOGS_DIR)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    if vendor_name == 'cache':
        # Special case for cache logging with dedicated file names
        read_logger = logging.getLogger(f'cache.read')
        read_logger.setLevel(logging.INFO)
        if not read_logger.handlers:
            handler = FlushingFileHandler(log_dir / 'cache_read.log')
            handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
            read_logger.addHandler(handler)
        
        write_logger = logging.getLogger(f'cache.write')
        write_logger.setLevel(logging.INFO)
        if not write_logger.handlers:
            handler = FlushingFileHandler(log_dir / 'cache_write.log')
            handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
            write_logger.addHandler(handler)
        
        return read_logger, write_logger
    else:
        # Standard vendor logging
        req_logger = logging.getLogger(f'vendor.{vendor_name}.req')
        req_logger.setLevel(logging.INFO)
        if not req_logger.handlers:
            handler = FlushingFileHandler(log_dir / f'vendor_{vendor_name}.log')
            handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
            req_logger.addHandler(handler)
        
        resp_logger = logging.getLogger(f'vendor.{vendor_name}.resp')
        resp_logger.setLevel(logging.INFO)
        if not resp_logger.handlers:
            handler = FlushingFileHandler(log_dir / f'vendor_{vendor_name}_resp.log')
            handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
            resp_logger.addHandler(handler)
        
        return req_logger, resp_logger


def create_accounting_loggers(log_dir: str = None) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Create accounting system loggers.
    
    Creates loggers for accounting system operations:
    - /tmp/gaia_logs/ceto/acct.log - Main accounting operations
    - /tmp/gaia_logs/ceto/acct_send.log - Recording API calls
    - /tmp/gaia_logs/ceto/acct_rcv.log - Cost calculations
    
    Returns:
        Tuple of (main_logger, send_logger, rcv_logger)
    """
    return create_dedicated_loggers('acct', log_dir)


def create_toolresponse_loggers(log_dir: str = None) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Create ToolResponse processing loggers.
    
    Creates loggers for ToolResponse handling and document operations:
    - /tmp/gaia_logs/ceto/toolresponse.log - Main ToolResponse processing
    - /tmp/gaia_logs/ceto/toolresponse_send.log - ToolResponse creation/flags
    - /tmp/gaia_logs/ceto/toolresponse_rcv.log - ToolResponse final processing
    
    Returns:
        Tuple of (main_logger, send_logger, rcv_logger)
    """
    return create_dedicated_loggers('toolresponse', log_dir)


def create_system_logger(log_dir: str = None) -> logging.Logger:
    """Create system logger for startup/shutdown/server events.
    
    Creates logger for system-wide events:
    - /tmp/gaia_logs/ceto/system.log - Startup/shutdown/server events
    
    Args:
        log_dir: Directory for log files
        
    Returns:
        System logger
    """
    log_dir = Path(log_dir or GAIA_SETTINGS.GAIA_LOGS_DIR)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    system_logger = logging.getLogger('system')
    system_logger.setLevel(logging.INFO)
    if not system_logger.handlers:
        handler = FlushingFileHandler(log_dir / 'system.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        system_logger.addHandler(handler)
    
    return system_logger