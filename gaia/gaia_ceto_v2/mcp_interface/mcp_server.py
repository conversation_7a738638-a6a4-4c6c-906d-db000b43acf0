"""
MCP Server - A local MCP server that also connects to external servers.

Level 0007 Extension: Includes comprehensive MCP call logging.
"""

import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any
import sys

from fastmcp import FastMCP

# Import core and tools using absolute imports
from gaia_ceto_v2.core.mcp_wrapper import log_mcp_tool_call
from gaia_ceto_v2.tools.tools import echostring as core_echostring

logger = logging.getLogger(__name__)

mcp = FastMCP("gaia_ceto_v2_mcp_server")

@mcp.tool()
def echostring(text: str) -> str:
    """
    Echoes the given text.
    """
    # Level 0007: Log MCP tool call
    with log_mcp_tool_call("echostring", {"text": text}, server_name="gaia_ceto_v2") as call:
        result = core_echostring(text)
        call.log_success(result)
        return result


class MCPServer:
    """
    A local MCP server that also manages external MCP servers.
    """

    def __init__(self, config_path: Path):
        """
        Initializes the MCPServer.

        Args:
            config_path: The path to the JSON configuration file.
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.processes: Dict[str, subprocess.Popen] = {}
        self.mcp = mcp

    def _load_config(self) -> Dict[str, Any]:
        """Loads the MCP server configuration from the JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"MCP config file not found at: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON from: {self.config_path}")
            return {}

    def start_all_external_servers(self):
        """Starts all enabled external MCP servers."""
        for server_name, server_config in self.config.get("mcpServers", {}).items():
            if server_config.get("enabled", False):
                self.start_external_server(server_name)

    def start_external_server(self, server_name: str, python_executable: str = "python"):
        """
        Starts a specific external MCP server.

        Args:
            server_name: The name of the server to start.
            python_executable: The path to the python executable.
        """
        if server_name in self.processes:
            logger.warning(f"Server '{server_name}' is already running.")
            return

        server_config = self.config.get("mcpServers", {}).get(server_name)
        if not server_config:
            logger.error(f"No configuration found for server: {server_name}")
            return

        command = [python_executable if server_config["command"] == "python" else server_config["command"]] + server_config.get("args", [])
        env = server_config.get("env", {})

        try:
            logger.info(f"Starting server: {server_name}")
            process = subprocess.Popen(command, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.processes[server_name] = process
        except FileNotFoundError:
            logger.error(f"Command not found for server '{server_name}': {command[0]}")
        except Exception as e:
            logger.error(f"Error starting server '{server_name}': {e}")

    def stop_all_external_servers(self):
        """Stops all running external MCP server processes."""
        for server_name in list(self.processes.keys()):
            self.stop_external_server(server_name)

    def stop_external_server(self, server_name: str):
        """
        Stops a specific external MCP server.

        Args:
            server_name: The name of the server to stop.
        """
        process = self.processes.get(server_name)
        if not process:
            logger.warning(f"Server '{server_name}' is not running.")
            return

        try:
            logger.info(f"Stopping server: {server_name}")
            process.terminate()
            process.wait(timeout=5)
            del self.processes[server_name]
        except subprocess.TimeoutExpired:
            logger.warning(f"Server '{server_name}' did not terminate gracefully. Killing.")
            process.kill()
            del self.processes[server_name]
        except Exception as e:
            logger.error(f"Error stopping server '{server_name}': {e}")

    async def run(self, host: str = "127.0.0.1", port: int = 9000):
        """
        Runs the local MCP server.
        """
        await self.mcp.run(host=host, port=port)

if __name__ == '__main__':
    import asyncio

    logging.basicConfig(level=logging.INFO)
    config_path = Path(__file__).parent / "server_config.json"
    server = MCPServer(config_path)
    server.start_all_external_servers()
    try:
        asyncio.run(server.run())
    except KeyboardInterrupt:
        logger.info("Shutting down server.")
    finally:
        server.stop_all_external_servers()
